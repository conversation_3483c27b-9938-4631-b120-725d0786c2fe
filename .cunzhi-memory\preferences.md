# 用户偏好设置

- 用户偏好：不要生成总结性Markdown文档，不要生成测试脚本，不要编译（用户自己编译），但可以帮助运行代码
- 用户偏好确认：不要生成总结性Markdown文档，不要生成测试脚本，不要编译（用户自己编译），但可以帮助运行代码进行验证
- 用户会自己处理Python版本兼容性适配，不需要AI添加兼容性代码，要求删除冗余的兼容性代码
- 用户选择方案2：Ragas + DeepEval组合评估方案，Ragas评估检索阶段（Stage 1），DeepEval评估NER阶段（Stage 2）
- 用户明确表示先只做评估功能，暂时不做反向优化，专注于评估框架的基础功能实现
- 用户建议可以切换环境升级numpy来解决评估框架的依赖问题
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，用户自己编译，只需要帮助运行
- 用户确认继续"先做评估，后续再考虑优化"的策略，专注于Ragas+DeepEval评估功能集成，暂不涉及反向优化功能
