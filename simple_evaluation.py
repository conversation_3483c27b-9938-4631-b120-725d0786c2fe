#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔬 简化评估框架 - 不依赖外部库的基础评估系统
专为元认知智能体NER系统设计的轻量级评估框架

核心功能：
1. 基础NER指标计算（F1, Precision, Recall）
2. 实体类型分析
3. 错误模式统计
4. 检索质量评估
5. 性能瓶颈识别
"""

import json
import logging
from typing import Dict, List, Any, Tuple, Set, Union
from dataclasses import dataclass
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)

@dataclass
class SimpleEvaluationResults:
    """简化评估结果数据类"""
    ner_metrics: Dict[str, float]
    retrieval_metrics: Dict[str, float]
    entity_analysis: Dict[str, Any]
    error_analysis: Dict[str, Any]
    optimization_suggestions: List[str]

class SimpleNERMetrics:
    """简化NER指标计算器"""
    
    @staticmethod
    def calculate_f1_score(predicted: Dict[str, List[str]], 
                          expected: Dict[str, List[str]]) -> <PERSON><PERSON>[float, float, float]:
        """计算F1分数"""
        pred_entities = set()
        exp_entities = set()
        
        # 将实体转换为(type, entity)元组集合
        for entity_type, entities in predicted.items():
            if isinstance(entities, (list, tuple)):
                for entity in entities:
                    pred_entities.add((entity_type, str(entity).lower().strip()))
            elif entities:  # 单个实体
                pred_entities.add((entity_type, str(entities).lower().strip()))

        for entity_type, entities in expected.items():
            if isinstance(entities, (list, tuple)):
                for entity in entities:
                    exp_entities.add((entity_type, str(entity).lower().strip()))
            elif entities:  # 单个实体
                exp_entities.add((entity_type, str(entities).lower().strip()))
        
        if not exp_entities and not pred_entities:
            return 1.0, 1.0, 1.0
        elif not exp_entities:
            return 0.0, 1.0, 0.0
        elif not pred_entities:
            return 1.0, 0.0, 0.0
        
        # 计算交集
        intersection = pred_entities & exp_entities
        
        precision = len(intersection) / len(pred_entities) if pred_entities else 0.0
        recall = len(intersection) / len(exp_entities) if exp_entities else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return precision, recall, f1
    
    @staticmethod
    def analyze_entity_types(test_cases: List[Dict]) -> Dict[str, Any]:
        """分析实体类型性能"""
        type_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
        
        for case in test_cases:
            predicted = case.get('predicted_entities', {})
            expected = case.get('true_entities', {})
            
            # 转换为集合便于比较
            pred_by_type = defaultdict(set)
            exp_by_type = defaultdict(set)
            
            for entity_type, entities in predicted.items():
                if isinstance(entities, (list, tuple)):
                    for entity in entities:
                        pred_by_type[entity_type].add(str(entity).lower().strip())
                elif entities:  # 单个实体
                    pred_by_type[entity_type].add(str(entities).lower().strip())

            for entity_type, entities in expected.items():
                if isinstance(entities, (list, tuple)):
                    for entity in entities:
                        exp_by_type[entity_type].add(str(entity).lower().strip())
                elif entities:  # 单个实体
                    exp_by_type[entity_type].add(str(entities).lower().strip())
            
            # 计算每种类型的TP, FP, FN
            all_types = set(pred_by_type.keys()) | set(exp_by_type.keys())
            
            for entity_type in all_types:
                pred_set = pred_by_type.get(entity_type, set())
                exp_set = exp_by_type.get(entity_type, set())
                
                tp = len(pred_set & exp_set)
                fp = len(pred_set - exp_set)
                fn = len(exp_set - pred_set)
                
                type_stats[entity_type]['tp'] += tp
                type_stats[entity_type]['fp'] += fp
                type_stats[entity_type]['fn'] += fn
        
        # 计算每种类型的指标
        type_metrics = {}
        for entity_type, stats in type_stats.items():
            tp, fp, fn = stats['tp'], stats['fp'], stats['fn']
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
            
            type_metrics[entity_type] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': tp + fn  # 真实实体数量
            }
        
        return type_metrics

class SimpleRetrievalMetrics:
    """简化检索指标计算器"""
    
    @staticmethod
    def calculate_retrieval_quality(stage1_data: List[Any],
                                  stage2_data: List[List[Dict]],
                                  ground_truth: List[Dict]) -> Dict[str, float]:
        """计算检索质量指标"""
        if not stage1_data or not stage2_data:
            return {'avg_examples_retrieved': 0.0, 'retrieval_success_rate': 0.0}
        
        total_examples = 0
        successful_retrievals = 0
        
        for i, examples in enumerate(stage2_data):
            if examples:  # 成功检索到示例
                successful_retrievals += 1
                total_examples += len(examples)
        
        avg_examples = total_examples / len(stage2_data) if stage2_data else 0.0
        success_rate = successful_retrievals / len(stage2_data) if stage2_data else 0.0
        
        return {
            'avg_examples_retrieved': avg_examples,
            'retrieval_success_rate': success_rate,
            'total_queries': len(stage1_data),
            'successful_retrievals': successful_retrievals
        }

class SimpleErrorAnalyzer:
    """简化错误分析器"""
    
    @staticmethod
    def analyze_common_errors(test_cases: List[Dict]) -> Dict[str, Any]:
        """分析常见错误模式"""
        error_patterns = {
            'missed_entities': defaultdict(int),  # 遗漏的实体
            'false_positives': defaultdict(int),  # 误报的实体
            'type_confusion': defaultdict(int),   # 类型混淆
            'boundary_errors': defaultdict(int)   # 边界错误
        }
        
        for case in test_cases:
            predicted = case.get('predicted_entities', {})
            expected = case.get('true_entities', {})
            
            # 转换为标准化格式
            pred_entities = set()
            exp_entities = set()
            
            for entity_type, entities in predicted.items():
                if isinstance(entities, (list, tuple)):
                    for entity in entities:
                        pred_entities.add((entity_type, str(entity).lower().strip()))
                elif entities:  # 单个实体
                    pred_entities.add((entity_type, str(entities).lower().strip()))

            for entity_type, entities in expected.items():
                if isinstance(entities, (list, tuple)):
                    for entity in entities:
                        exp_entities.add((entity_type, str(entity).lower().strip()))
                elif entities:  # 单个实体
                    exp_entities.add((entity_type, str(entities).lower().strip()))
            
            # 分析遗漏的实体
            missed = exp_entities - pred_entities
            for entity_type, entity in missed:
                error_patterns['missed_entities'][entity_type] += 1
            
            # 分析误报的实体
            false_pos = pred_entities - exp_entities
            for entity_type, entity in false_pos:
                error_patterns['false_positives'][entity_type] += 1
            
            # 分析类型混淆（相同实体不同类型）
            pred_entities_only = {entity for _, entity in pred_entities}
            exp_entities_only = {entity for _, entity in exp_entities}
            
            common_entities = pred_entities_only & exp_entities_only
            for entity in common_entities:
                pred_types = {t for t, e in pred_entities if e == entity}
                exp_types = {t for t, e in exp_entities if e == entity}
                
                if pred_types != exp_types:
                    for wrong_type in pred_types - exp_types:
                        for correct_type in exp_types:
                            error_patterns['type_confusion'][f"{wrong_type}->{correct_type}"] += 1
        
        return {k: dict(v) for k, v in error_patterns.items()}

class SimpleOptimizer:
    """简化优化建议生成器"""
    
    @staticmethod
    def generate_suggestions(ner_metrics: Dict[str, float],
                           retrieval_metrics: Dict[str, float],
                           entity_analysis: Dict[str, Any],
                           error_analysis: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # NER性能建议
        overall_f1 = ner_metrics.get('f1', 0.0)
        precision = ner_metrics.get('precision', 0.0)
        recall = ner_metrics.get('recall', 0.0)
        
        if overall_f1 < 0.7:
            suggestions.append("🧠 整体NER性能较低，建议全面优化")
        
        if precision < 0.7:
            suggestions.append("🎯 精确率较低，建议：1) 优化NER prompt模板; 2) 提高few-shot示例质量; 3) 调整temperature参数")
        
        if recall < 0.7:
            suggestions.append("🔍 召回率较低，建议：1) 增加few-shot示例数量; 2) 扩大检索范围; 3) 优化实体识别策略")
        
        # 检索性能建议
        success_rate = retrieval_metrics.get('retrieval_success_rate', 0.0)
        avg_examples = retrieval_metrics.get('avg_examples_retrieved', 0.0)
        
        if success_rate < 0.9:
            suggestions.append("🔍 检索成功率较低，建议：1) 检查向量库质量; 2) 优化查询生成; 3) 调整检索参数")
        
        if avg_examples < 2:
            suggestions.append("📚 检索示例数量不足，建议：1) 增加vector_top_k; 2) 降低score_threshold; 3) 扩充训练数据")
        
        # 实体类型特定建议
        if entity_analysis:
            worst_types = []
            for entity_type, metrics in entity_analysis.items():
                if metrics.get('f1', 0.0) < 0.5:
                    worst_types.append(entity_type)
            
            if worst_types:
                suggestions.append(f"🏷️ 以下实体类型性能较差: {', '.join(worst_types)}，建议针对性优化")
        
        # 错误模式建议
        if error_analysis:
            missed = error_analysis.get('missed_entities', {})
            false_pos = error_analysis.get('false_positives', {})
            type_confusion = error_analysis.get('type_confusion', {})
            
            if missed:
                top_missed = sorted(missed.items(), key=lambda x: x[1], reverse=True)[:3]
                suggestions.append(f"❌ 经常遗漏的实体类型: {', '.join([t for t, _ in top_missed])}，建议增强识别")
            
            if false_pos:
                top_false = sorted(false_pos.items(), key=lambda x: x[1], reverse=True)[:3]
                suggestions.append(f"⚠️ 经常误报的实体类型: {', '.join([t for t, _ in top_false])}，建议提高精确度")
            
            if type_confusion:
                top_confusion = sorted(type_confusion.items(), key=lambda x: x[1], reverse=True)[:3]
                suggestions.append(f"🔄 常见类型混淆: {', '.join([c for c, _ in top_confusion])}，建议强化类型区分")
        
        return suggestions

class SimpleEvaluationFramework:
    """简化评估框架"""
    
    def __init__(self):
        self.ner_metrics = SimpleNERMetrics()
        self.retrieval_metrics = SimpleRetrievalMetrics()
        self.error_analyzer = SimpleErrorAnalyzer()
        self.optimizer = SimpleOptimizer()
        
        logger.info("🔬 简化评估框架初始化完成")
    
    def evaluate_full_pipeline(self, stage1_data: List[Any],
                             stage2_data: List[List[Dict]],
                             stage3_data: List[Dict],
                             ground_truth: List[Dict]) -> SimpleEvaluationResults:
        """完整流水线评估"""
        try:
            logger.info("🚀 开始简化评估...")

            # 调试信息
            logger.info(f"Stage1 data type: {type(stage1_data)}, length: {len(stage1_data) if hasattr(stage1_data, '__len__') else 'N/A'}")
            logger.info(f"Stage2 data type: {type(stage2_data)}, length: {len(stage2_data) if hasattr(stage2_data, '__len__') else 'N/A'}")
            logger.info(f"Stage3 data type: {type(stage3_data)}, length: {len(stage3_data) if hasattr(stage3_data, '__len__') else 'N/A'}")
            logger.info(f"Ground truth type: {type(ground_truth)}, length: {len(ground_truth) if hasattr(ground_truth, '__len__') else 'N/A'}")

            # 准备NER测试用例
            ner_test_cases = []
            for i, result in enumerate(stage3_data):
                if i < len(ground_truth):
                    ner_test_cases.append({
                        'predicted_entities': result,
                        'true_entities': ground_truth[i].get('entities', {})
                    })
            
            # 计算整体NER指标
            total_precision, total_recall, total_f1 = 0.0, 0.0, 0.0
            valid_cases = 0
            
            for case in ner_test_cases:
                precision, recall, f1 = self.ner_metrics.calculate_f1_score(
                    case['predicted_entities'], case['true_entities']
                )
                if not (precision == 0 and recall == 0 and f1 == 0):  # 排除无效案例
                    total_precision += precision
                    total_recall += recall
                    total_f1 += f1
                    valid_cases += 1
            
            ner_metrics = {
                'precision': total_precision / valid_cases if valid_cases > 0 else 0.0,
                'recall': total_recall / valid_cases if valid_cases > 0 else 0.0,
                'f1': total_f1 / valid_cases if valid_cases > 0 else 0.0,
                'total_cases': len(ner_test_cases),
                'valid_cases': valid_cases
            }
            
            # 计算检索指标
            retrieval_metrics = self.retrieval_metrics.calculate_retrieval_quality(
                stage1_data, stage2_data, ground_truth
            )
            
            # 实体类型分析
            entity_analysis = self.ner_metrics.analyze_entity_types(ner_test_cases)
            
            # 错误分析
            error_analysis = self.error_analyzer.analyze_common_errors(ner_test_cases)
            
            # 生成优化建议
            suggestions = self.optimizer.generate_suggestions(
                ner_metrics, retrieval_metrics, entity_analysis, error_analysis
            )
            
            results = SimpleEvaluationResults(
                ner_metrics=ner_metrics,
                retrieval_metrics=retrieval_metrics,
                entity_analysis=entity_analysis,
                error_analysis=error_analysis,
                optimization_suggestions=suggestions
            )
            
            logger.info("✅ 简化评估完成")
            return results
            
        except Exception as e:
            logger.error(f"❌ 简化评估失败: {e}")
            raise

# 全局实例
_simple_evaluation_framework = None

def get_simple_evaluation_framework():
    """获取简化评估框架实例"""
    global _simple_evaluation_framework
    if _simple_evaluation_framework is None:
        _simple_evaluation_framework = SimpleEvaluationFramework()
    return _simple_evaluation_framework
